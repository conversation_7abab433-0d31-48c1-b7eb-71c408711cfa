// shared/UI/components/modal/UserManagementModalForm.tsx
"use client";

import React, { useState, useEffect } from 'react';
import { UserDetailsData } from '@/shared/types/user-management-types';
import { CreateUserData, EditUserData, useCurrenciesQuery } from '@/shared/query';
import { getDefaultCurrencyId } from '@/shared/constants/currencyConstants';
import { UserManagementModalMode } from './UserManagementModal';
import { PrimaryButton, AccessToggleSwitch } from '@/shared/UI/components';
import QRCode from 'qrcode';

import PhoneInput from 'react-phone-number-input';
import 'react-phone-number-input/style.css';
import Image from 'next/image';

interface UserManagementModalFormProps {
  mode: UserManagementModalMode;
  userData?: UserDetailsData;
  onSubmit: (formData: CreateUserData | EditUserData) => Promise<void>;
  onCancel?: () => void;
  isLoading?: boolean;
}

/**
 * UserManagementModalForm Component
 * 
 * A simplified form component specifically designed for the UserManagementModal.
 * Contains essential fields for user creation and editing with proper validation
 * and dark theme styling.
 * 
 * Features:
 * - Essential fields: email, firstName, lastName, phone, currency selection
 * - Role and status management
 * - Form validation
 * - Dark theme styling matching modal design
 * - Integration with existing user management API
 */
const UserManagementModalForm: React.FC<UserManagementModalFormProps> = ({
  mode,
  userData,
  onSubmit,
  onCancel,
  isLoading = false
}) => {
  // Fetch available currencies
  const { data: currenciesResponse, isLoading: isCurrenciesLoading } = useCurrenciesQuery();
  // Form state
  const [formData, setFormData] = useState<CreateUserData | EditUserData>(() => {
    if (mode === 'edit' && userData) {
      return {
        id: Number(userData.id),
        firstName: userData.firstName || '',
        lastName: userData.lastName || '',
        email: userData.email || '',
        phone: userData.phone || '',
        phoneCode: userData.phoneCode || '+94',
        zipCode: '', // Not available in UserDetailsData
        dateOfBirth: '', // Not available in UserDetailsData
        countryCode: 'LK', // Default value
        currencyId: Number(userData.currencyId) || getDefaultCurrencyId(),
        vipLevel: Number(userData.playerCategoryLevel) || 1,
        city: '', // Not available in UserDetailsData
        emailVerified: userData.emailVerified || false,
        phoneVerified: userData.phoneVerified || false,

        markAsBot: false, // Not available in UserDetailsData
        active: userData.active !== false,
        demo: userData.demo || false,
        userType: userData.userType || 1,

      } as EditUserData;
    }

    // Default create form data
    return {
      nickName: '',
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      phoneCode: '+94',
      zipCode: '',
      dateOfBirth: '',
      countryCode: 'LK',
      currencyId: getDefaultCurrencyId(),
      activeBonusId: null,
      vipLevel: 1,
      city: '',
      emailVerified: false,
      phoneVerified: false,

      markAsBot: false,
      active: true,
      demo: false,
      affiliatedData: '',
      nationalId: null,
      clickId: null,
      wyntaClickId: null,
      categoryType: null,
      userType: 1,

      rfidToken: '',
    } as CreateUserData;
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [name, setName] = useState('');
  const [mobileNumber, setMobileNumber] = useState('');
  const [rfidToken, setRfidToken] = useState('');
  const [kioskAccess, setKioskAccess] = useState(false);
  const [websiteAccess, setWebsiteAccess] = useState(true); // Default to website access

  // QR Code state for edit mode
  const [qrCodeDataUrl, setQrCodeDataUrl] = useState<string>('');
  const [isGeneratingQR, setIsGeneratingQR] = useState(false);

  // Update form data when userData changes (for edit mode) or currencies load (for create mode)
  useEffect(() => {
    if (mode === 'edit' && userData) {
      // Combine firstName and lastName for the name field
      const fullName = `${userData.firstName || ''} ${userData.lastName || ''}`.trim();
      setName(fullName);
      setMobileNumber(userData.phone || '');
      setRfidToken(userData.rfidToken || '');

      // Set access toggles based on userType
      const userType = userData.userType || 1;
      setKioskAccess(userType === 2 || userType === 3);
      setWebsiteAccess(userType === 1 || userType === 3);

      setFormData(prev => ({
        ...prev,
        id: Number(userData.id),
        firstName: userData.firstName || '',
        lastName: userData.lastName || '',
        email: userData.email || '',
        phone: userData.phone || '',
        phoneCode: userData.phoneCode || '+94',
        currencyId: Number(userData.currencyId) || getDefaultCurrencyId(),
        userType: userData.userType || 1,
        active: userData.active !== false,
      }));

      // Generate QR code if qrCode data is available
      if (userData.qrCode) {
        generateQRCode(userData.qrCode);
      }
    } else if (mode === 'create' && currenciesResponse?.data && currenciesResponse.data.length > 0) {
      // For create mode, automatically select the first available currency
      const firstCurrency = currenciesResponse.data[0];
      setFormData(prev => ({
        ...prev,
        currencyId: firstCurrency.id,
      }));
    }
  }, [mode, userData, currenciesResponse]);

  // QR Code generation function
  const generateQRCode = async (qrData: string) => {
    if (!qrData) return;

    setIsGeneratingQR(true);
    try {
      const qrCodeDataUrl = await QRCode.toDataURL(qrData, {
        width: 200,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        }
      });
      setQrCodeDataUrl(qrCodeDataUrl);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to generate QR code:', error);
    } finally {
      setIsGeneratingQR(false);
    }
  };

  // Print QR Code function
  const handlePrintQRCode = async () => {
    if (!qrCodeDataUrl || !userData) return;

    try {
      // Create a simple user QR code print template
      const userQRTemplate = `
        <!DOCTYPE html>
        <html>
          <head>
            <title>User QR Code</title>
            <style>
              body {
                font-family: Arial, sans-serif;
                margin: 20px;
                text-align: center;
              }
              .qr-container {
                padding: 20px;
                border: 1px solid #ccc;
                border-radius: 8px;
                display: inline-block;
              }
              .user-info {
                margin-bottom: 20px;
              }
              .qr-code {
                margin: 20px 0;
              }
              .timestamp {
                font-size: 12px;
                color: #666;
                margin-top: 20px;
              }
            </style>
          </head>
          <body>
            <div class="qr-container">
              <h2>User QR Code</h2>
              <div class="user-info">
                <div><strong>User:</strong> ${userData.firstName} ${userData.lastName || ''}</div>
                <div><strong>Username:</strong> ${userData.userName}</div>
                <div><strong>User ID:</strong> ${userData.id}</div>
              </div>
              <div class="qr-code">
                <img src="${qrCodeDataUrl}" alt="User QR Code" style="width: 200px; height: 200px; border: 1px solid #ccc;" />
              </div>
              <div class="timestamp">
                Generated on ${new Date().toLocaleString()}
              </div>
            </div>
          </body>
        </html>
      `;

      // Open print window
      const printWindow = window.open('', '_blank', 'width=800,height=600');
      if (printWindow) {
        printWindow.document.write(userQRTemplate);
        printWindow.document.close();
        printWindow.focus();
        printWindow.print();
      }
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to print QR code:', error);
    }
  };

  // Handle input changes
  const handleInputChange = (field: string) => (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const value = e.target.type === 'checkbox' ? (e.target as HTMLInputElement).checked : e.target.value;
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  // Handle name change
  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setName(e.target.value);
    if (errors.name) {
      setErrors(prev => ({
        ...prev,
        name: ''
      }));
    }
  };

  // Handle mobile number change (for PhoneInput component)
  const handleMobileNumberChange = (value: string | undefined) => {
    setMobileNumber(value || '');
    if (errors.mobileNumber) {
      setErrors(prev => ({
        ...prev,
        mobileNumber: ''
      }));
    }
  };

  // Handle RFID token change
  const handleRfidTokenChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setRfidToken(e.target.value);
    if (errors.rfidToken) {
      setErrors(prev => ({
        ...prev,
        rfidToken: ''
      }));
    }
  };

  // Calculate userType based on access toggles
  const calculateUserType = (): number => {
    if (kioskAccess && websiteAccess) return 3; // Kiosk & Online
    if (kioskAccess) return 2; // Kiosk only
    return 1; // Online only (default)
  };

  // Split name into firstName and lastName
  const splitName = (fullName: string): { firstName: string; lastName: string } => {
    const nameParts = fullName.trim().split(' ');
    if (nameParts.length === 1) {
      return { firstName: nameParts[0], lastName: '' };
    }
    const firstName = nameParts[0];
    const lastName = nameParts.slice(1).join(' ');
    return { firstName, lastName };
  };

  // Validate form
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Name validation (optional field, no validation needed)

    // Mobile number validation (optional field, no validation needed)

    // Currency validation
    if (!formData.currencyId) {
      newErrors.currencyId = 'Currency is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      // Split name into firstName and lastName
      const { firstName } = splitName(name);

      if (mode === 'create') {
        // For create mode, send only required fields
        const dataToSubmit: CreateUserData = {
          firstName,
          phone: mobileNumber,
          phoneCode: formData.phoneCode || '+94',
          currencyId: Number(formData.currencyId),
          userType: calculateUserType(),
          rfidToken: rfidToken.trim() || '',
          dateOfBirth: 'dateOfBirth' in formData ? formData.dateOfBirth || '' : ''
        };

        await onSubmit(dataToSubmit);
      } else if (mode === 'edit') {
        // For edit mode, send only required fields
        // Type guard to ensure formData has id property
        if (!('id' in formData) || !formData.id) {
          throw new Error('User ID is required for edit mode');
        }

        const dataToSubmit: EditUserData = {
          id: Number(formData.id),
          firstName,
          phone: mobileNumber,
          phoneCode: formData.phoneCode || '+94',
          currencyId: Number(formData.currencyId),
          userType: calculateUserType(),
          rfidToken: rfidToken.trim() || ''
        };

        await onSubmit(dataToSubmit);
      }
    } catch (error) {
      onCancel?.();
      // eslint-disable-next-line no-console
      console.error('Failed to submit user form', error);
    }
  };

  // Get action button text
  const getActionButtonText = () => {
    if (isLoading) {
      return mode === 'create' ? 'Creating...' : 'Updating...';
    }
    return mode === 'create' ? 'Create User' : 'Update User';
  };

  return (
    <>
      {/* Custom styles for PhoneInput component */}
      <style jsx>{`
        .phone-input-custom .PhoneInputInput {
          background-color: var(--elevated-bg);
          border: 1px solid var(--border-secondary);
          border-radius: 8px;
          height: 43px;
          padding: 8px 12px;
          color: white;
          font-size: 14px;
        }
        .phone-input-custom .PhoneInputInput:focus {
          outline: none;
          border-color: var(--primary-400);
          box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.2);
        }
        .phone-input-custom .PhoneInputInput::placeholder {
          color: #9ca3af;
        }
        .phone-input-custom .PhoneInputCountrySelect {
          background-color: var(--elevated-bg);
          border: 1px solid var(--border-secondary);
          border-radius: 8px 0 0 8px;
          border-right: none;
          padding: 8px;
        }
        .phone-input-custom .PhoneInputCountrySelect:focus {
          outline: none;
          border-color: var(--primary-400);
        }
        .phone-input-custom.border-red-500 .PhoneInputInput,
        .phone-input-custom.border-red-500 .PhoneInputCountrySelect {
          border-color: #ef4444;
        }
      `}</style>

      <form onSubmit={handleSubmit} className="flex flex-col h-full" style={{ gap: '16px' }}>
        {/* Form Fields */}
        <div className="flex-1 overflow-y-auto">
          <div style={{ gap: '16px' }} className="flex flex-col">
            {/* Currency Selection Field - Only show in edit mode, automatically selected in create mode */}
            {mode === 'edit' && (
              <div className="flex flex-col gap-[8px]">
                <label
                  className="font-rubik font-semibold text-white capitalize"
                  style={{ fontSize: '14px', fontWeight: 600 }}
                >
                  Currency <span className="text-red-500">*</span>
                </label>
                <select
                  className={`w-full h-[43px] bg-elevated border rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-primary-400 focus:border-transparent ${errors.currencyId ? 'border-red-500' : 'border-border-secondary'
                    }`}
                  value={formData.currencyId || ''}
                  onChange={handleInputChange('currencyId')}
                  disabled={isCurrenciesLoading}
                >
                  <option value="" disabled>
                    {isCurrenciesLoading ? 'Loading currencies...' : 'Select currency'}
                  </option>
                  {currenciesResponse?.data?.map((currency) => (
                    <option key={currency.id} value={currency.id}>
                      {currency.name} ({currency.code})
                    </option>
                  ))}
                </select>
                {errors.currencyId && (
                  <p className="text-red-500 text-sm">{errors.currencyId}</p>
                )}
              </div>
            )}

            {/* Name Field */}
            <div className="flex flex-col gap-[8px]">
              <label
                className="font-rubik font-semibold text-white capitalize"
                style={{ fontSize: '14px', fontWeight: 600 }}
              >
                Name
              </label>
              <input
                type="text"
                className={`w-full bg-elevated h-[43px] border rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-400 focus:border-transparent ${errors.name ? 'border-red-500' : 'border-border-secondary'
                  }`}
                placeholder="Enter full name"
                value={name}
                onChange={handleNameChange}
              />
              {errors.name && (
                <p className="text-red-500 text-sm">{errors.name}</p>
              )}
            </div>

            {/* Mobile Number Field with Country Code */}
            <div className="flex flex-col gap-[8px]">
              <label
                className="font-rubik font-semibold text-white capitalize"
                style={{ fontSize: '14px', fontWeight: 600 }}
              >
                Mobile Number
              </label>
              <PhoneInput
                placeholder="Enter mobile number"
                value={mobileNumber}
                onChange={handleMobileNumberChange}
                defaultCountry="LK"
                className={`phone-input-custom ${errors.mobileNumber ? 'border-red-500' : ''}`}
                style={{
                  '--PhoneInputCountryFlag-height': '1em',
                  '--PhoneInputCountrySelectArrow-color': '#9ca3af',
                  '--PhoneInput-color--focus': '#f59e0b',
                } as React.CSSProperties}
              />
              {errors.mobileNumber && (
                <p className="text-red-500 text-sm">{errors.mobileNumber}</p>
              )}
            </div>

            {/* Username Field (Read-only for edit mode) */}
            {mode === 'edit' && userData && (
              <div className="flex flex-col gap-[8px]">
                <label
                  className="font-rubik font-semibold text-white capitalize"
                  style={{ fontSize: '14px', fontWeight: 600 }}
                >
                  Username
                </label>
                <input
                  type="text"
                  className="w-full h-[43px] bg-elevated border border-border-secondary rounded-lg px-3 py-2 text-gray-400 cursor-not-allowed"
                  value={userData.userName || ''}
                  readOnly
                  disabled
                />
              </div>
            )}

            {/* RFID Field */}
            <div className="flex flex-col gap-[8px]">
              <label
                className="font-rubik font-semibold text-white capitalize"
                style={{ fontSize: '14px', fontWeight: 600 }}
              >
                RFID
              </label>
              <input
                type="text"
                className={`w-full h-[43px] bg-elevated border rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-400 focus:border-transparent ${errors.rfidToken ? 'border-red-500' : 'border-border-secondary'
                  }`}
                placeholder="Enter RFID token (optional)"
                value={rfidToken}
                onChange={handleRfidTokenChange}
              />
              {errors.rfidToken && (
                <p className="text-red-500 text-sm">{errors.rfidToken}</p>
              )}
            </div>

            {/* QR Code Display (Edit mode only) */}
            {mode === 'edit' && userData && (
              <div className="flex flex-col gap-[8px]">
                <label
                  className="font-rubik font-semibold text-white capitalize"
                  style={{ fontSize: '14px', fontWeight: 600 }}
                >
                  QR Code
                </label>
                <div className="bg-elevated border border-border-secondary rounded-lg p-4">
                  {isGeneratingQR ? (
                    <div className="flex items-center justify-center h-48">
                      <div className="text-white">Generating QR Code...</div>
                    </div>
                  ) : qrCodeDataUrl ? (
                    <div className="flex flex-col items-center gap-3">
                      <Image
                        src={qrCodeDataUrl}
                        alt="User QR Code"
                        width={48}
                        height={48}
                        className="w-48 h-48 border border-gray-300 rounded"
                      />
                      <button
                        type="button"
                        onClick={handlePrintQRCode}
                        className="ti-btn ti-btn-sm ti-btn-primary ti-btn-wave"
                        title="Print QR Code"
                      >
                        <i className="ri-printer-line me-1"></i>
                        Print QR Code
                      </button>
                    </div>
                  ) : (
                    <div className="flex items-center justify-center h-48 text-gray-400">
                      No QR Code available
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Access Control Toggles */}
            <div className="space-y-4">

              <div className="flex justify-around">
                {/* Kiosk Access Toggle */}
                <AccessToggleSwitch
                  id="kiosk-access"
                  label="Kiosk Access"
                  icon="ri-computer-line"
                  checked={kioskAccess}
                  onChange={setKioskAccess}
                />

                {/* Website Access Toggle */}
                <AccessToggleSwitch
                  id="website-access"
                  label="Website Access"
                  icon="ri-global-line"
                  checked={websiteAccess}
                  onChange={setWebsiteAccess}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Action Button */}
        <div className="mt-auto">
          <PrimaryButton
            type="submit"
            className="w-full"
            loading={isLoading}
            disabled={isLoading}
          >
            {getActionButtonText()}
          </PrimaryButton>
        </div>
      </form>
    </>
  );
};

export default UserManagementModalForm;
